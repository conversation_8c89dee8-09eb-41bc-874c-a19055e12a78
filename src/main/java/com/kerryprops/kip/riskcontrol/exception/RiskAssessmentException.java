package com.kerryprops.kip.riskcontrol.exception;

import com.kerryprops.kip.riskcontrol.constant.RiskResult;
import com.kerryprops.kip.riskcontrol.dto.IpGeolocationInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * Base class for risk assessment exceptions.
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RiskAssessmentException extends RuntimeException {

    private final RiskResult riskResult;
    private final String blockMessage;
    private final String assessmentDetails;
    private final LocalDateTime assessmentTime;
    private final IpGeolocationInfo ipGeolocation;

    public RiskAssessmentException(String message, RiskResult riskResult,
                                   String blockMessage, String assessmentDetails,
                                   LocalDateTime assessmentTime, IpGeolocationInfo ipGeolocation) {
        super(message);
        this.riskResult = riskResult;
        this.blockMessage = blockMessage;
        this.assessmentDetails = assessmentDetails;
        this.assessmentTime = assessmentTime;
        this.ipGeolocation = ipGeolocation;
    }

    public RiskAssessmentException(String message, Throwable cause) {
        super(message, cause);
        this.riskResult = RiskResult.REJECT;
        this.blockMessage = "系统繁忙，请稍后重试";
        this.assessmentDetails = "系统异常：" + cause.getMessage();
        this.assessmentTime = LocalDateTime.now();
        this.ipGeolocation = null;
    }
}
