package com.kerryprops.kip.riskcontrol.controller;

import com.kerryprops.kip.riskcontrol.dto.marketing.MarketingRiskAssessRequest;
import com.kerryprops.kip.riskcontrol.dto.marketing.MarketingRiskAssessResponse;
import com.kerryprops.kip.riskcontrol.service.marketing.MarketingRiskAssessmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller for marketing scenario risk assessment.
 */
@Slf4j
@RestController
@RequestMapping("/api/risk-assessment/marketing")
@RequiredArgsConstructor
@Tag(name = "Marketing Risk Assessment", description = "Marketing scenario risk assessment endpoints")
public class MarketingRiskAssessmentController {

    private final MarketingRiskAssessmentService marketingRiskAssessmentService;

    @Operation(
            summary = "Assess marketing risk",
            description = "Assess risk for marketing scenarios (rewards/scene triggers/bonuses/coupons)"
    )
    @PostMapping
    public MarketingRiskAssessResponse assessMarketingRisk(@Valid @RequestBody MarketingRiskAssessRequest request) {
        log.info("Received marketing risk assessment request - mall: {}, points: {}",
                request.getMallCode(), request.getMemberPoints());

        MarketingRiskAssessResponse response = marketingRiskAssessmentService.assessMarketingRisk(request);

        log.info("Completed marketing risk assessment - mall: {}, result: {}",
                request.getMallCode(), response.getRiskResult());

        return response;
    }
}
