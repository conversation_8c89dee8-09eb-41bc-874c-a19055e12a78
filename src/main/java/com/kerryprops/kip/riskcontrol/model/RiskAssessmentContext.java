package com.kerryprops.kip.riskcontrol.model;

import com.kerryprops.kip.riskcontrol.constant.RiskResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 综合风控评估上下文模型.
 * 包含所有风控评估所需的信息，支持多种业务场景的风控规则.
 *
 * <AUTHOR> Properties Limited
 * @since 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RiskAssessmentContext {

    /**
     * 业务场景.
     */
    private BusinessScenario businessScenario;
    /**
     * 商场代码.
     */
    private MallCode mallCode;

    // ========== 基础信息 ==========
    /**
     * 用户ID.
     */
    private String userId;
    /**
     * UnionID.
     */
    private String unionId;
    /**
     * IP地址.
     */
    private String ipAddress;
    /**
     * IP地理位置信息.
     */
    private IpGeolocation ipGeolocation;

    // ========== IP相关信息 ==========
    /**
     * 手机号.
     */
    private String phoneNumber;
    /**
     * 是否为大陆手机号.
     */
    private Boolean isMainlandPhoneNumber;

    // ========== 手机号相关信息 ==========
    /**
     * 是否为虚拟号段.
     */
    private Boolean isVirtualPhoneNumber;
    /**
     * 会员积分.
     */
    private Integer memberPoints;
    /**
     * 是否为冻结会员.
     */
    private Boolean isFrozenMember;

    // ========== 会员相关信息 ==========
    /**
     * 腾讯风控评估结果.
     */
    private String tencentRiskLevel; // "高", "中", "低"
    /**
     * ID校验结果 - 一个月内同一UnionID登录次数.
     */
    private Integer unionIdLoginCount;

    // ========== 风控相关信息 ==========
    /**
     * ID校验结果 - 一个月内同一手机号登录次数.
     */
    private Integer phoneNumberLoginCount;
    /**
     * 风控评估结果.
     */
    private RiskResult riskResult;
    /**
     * 拦截提示语.
     */
    private String blockMessage;

    // ========== 评估结果 ==========
    /**
     * 评估详情.
     */
    private String assessmentDetails;

    /**
     * 创建风控评估上下文.
     *
     * @param businessScenario 业务场景
     * @param mallCode         商场代码
     * @return RiskAssessmentContext
     */
    public static RiskAssessmentContext create(BusinessScenario businessScenario, MallCode mallCode) {
        RiskAssessmentContext context = new RiskAssessmentContext();
        context.setBusinessScenario(businessScenario);
        context.setMallCode(mallCode);
        return context;
    }

    /**
     * 设置IP相关信息.
     *
     * @param ipAddress     IP地址
     * @param ipGeolocation IP地理位置
     * @return this
     */
    public RiskAssessmentContext withIpInfo(String ipAddress, IpGeolocation ipGeolocation) {
        this.ipAddress = ipAddress;
        this.ipGeolocation = ipGeolocation;
        return this;
    }

    /**
     * 设置手机号相关信息.
     *
     * @param phoneNumber           手机号
     * @param isMainlandPhoneNumber 是否为大陆手机号
     * @param isVirtualPhoneNumber  是否为虚拟号段
     * @return this
     */
    public RiskAssessmentContext withPhoneInfo(String phoneNumber, Boolean isMainlandPhoneNumber, 
            Boolean isVirtualPhoneNumber) {
        this.phoneNumber = phoneNumber;
        this.isMainlandPhoneNumber = isMainlandPhoneNumber;
        this.isVirtualPhoneNumber = isVirtualPhoneNumber;
        return this;
    }

    /**
     * 设置会员相关信息.
     *
     * @param memberPoints   会员积分
     * @param isFrozenMember 是否为冻结会员
     * @return this
     */
    public RiskAssessmentContext withMemberInfo(Integer memberPoints, Boolean isFrozenMember) {
        this.memberPoints = memberPoints;
        this.isFrozenMember = isFrozenMember;
        return this;
    }

    /**
     * 设置风控相关信息.
     *
     * @param tencentRiskLevel      腾讯风控等级
     * @param unionIdLoginCount     UnionID登录次数
     * @param phoneNumberLoginCount 手机号登录次数
     * @return this
     */
    public RiskAssessmentContext withRiskInfo(String tencentRiskLevel, Integer unionIdLoginCount, 
            Integer phoneNumberLoginCount) {
        this.tencentRiskLevel = tencentRiskLevel;
        this.unionIdLoginCount = unionIdLoginCount;
        this.phoneNumberLoginCount = phoneNumberLoginCount;
        return this;
    }

    /**
     * 检查IP是否来自大陆.
     *
     * @return true if IP is from mainland China
     */
    public boolean isIpFromMainlandChina() {
        return ipGeolocation != null && ipGeolocation.isFromMainlandChina();
    }

    /**
     * 检查是否有负积分.
     *
     * @return true if member has negative points
     */
    public boolean hasNegativePoints() {
        return memberPoints != null && memberPoints < 0;
    }

    /**
     * 检查ID校验是否超限.
     *
     * @return true if ID validation exceeds limits
     */
    public boolean isIdValidationExceeded() {
        return (unionIdLoginCount != null && unionIdLoginCount >= 1) ||
                (phoneNumberLoginCount != null && phoneNumberLoginCount >= 3);
    }

    /**
     * 检查腾讯风控是否为高风险.
     *
     * @return true if Tencent risk level is high
     */
    public boolean isTencentRiskHigh() {
        return "高".equals(tencentRiskLevel);
    }

    /**
     * 检查腾讯风控是否为中风险.
     *
     * @return true if Tencent risk level is medium
     */
    public boolean isTencentRiskMedium() {
        return "中".equals(tencentRiskLevel);
    }

    /**
     * 业务场景类型.
     */
    public enum BusinessScenario {
        /** 登录. */
        LOGIN("登录"),
        /** 会员停车权益. */
        PARKING_BENEFIT("会员停车权益"),
        /** 拍照积分. */
        PHOTO_POINTS("拍照积分"),
        /** 营销场景. */
        MARKETING("营销场景"),
        /** 销售积分. */
        SALES_POINTS("销售积分"),
        /** 核销券. */
        COUPON_VERIFICATION("核销券");

        /** 描述. */
        private final String description;

        BusinessScenario(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 商场代码枚举.
     */
    public enum MallCode {
        /** JAKC商场. */
        JAKC("JAKC"),
        /** HKC商场. */
        HKC("HKC"),
        /** KP商场. */
        KP("KP"),
        /** 所有商场. */
        ALL("All");

        /** 商场代码. */
        private final String code;

        MallCode(String code) {
            this.code = code;
        }

        public String getCode() {
            return code;
        }
    }
}
