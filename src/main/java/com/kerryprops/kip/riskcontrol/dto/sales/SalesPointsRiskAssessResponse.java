package com.kerryprops.kip.riskcontrol.dto.sales;

import com.kerryprops.kip.riskcontrol.constant.RiskResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Sales points risk assessment response.
 */
@Data
@Schema(description = "Sales points risk assessment response")
public class SalesPointsRiskAssessResponse {

    @Schema(description = "Mall code", example = "JAKC")
    private String mallCode;

    @Schema(description = "Member points", example = "1000")
    private Integer memberPoints;

    @Schema(description = "Risk assessment result", example = "PASS")
    private RiskResult riskResult;

    @Schema(description = "Block message if rejected")
    private String blockMessage;

    @Schema(description = "Assessment details", example = "销售积分：会员状态正常")
    private String assessmentDetails;

    @Schema(description = "Assessment time")
    private LocalDateTime assessmentTime;
}
