package com.kerryprops.kip.riskcontrol.dto.login;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * Login scenario risk assessment request.
 */
@Data
@Schema(description = "Login scenario risk assessment request")
public class LoginRiskAssessRequest {

    @NotBlank(message = "Mall code is required")
    @Schema(
            description = "Mall code",
            example = "JAKC",
            allowableValues = {"JAKC", "HKC", "KP", "ALL"},
            required = true
    )
    private String mallCode;

    @Schema(description = "IP address", example = "************")
    private String ipAddress;

    @Schema(description = "Phone number", example = "13800138000")
    private String phoneNumber;

    @Schema(description = "UnionID", example = "unionid123")
    private String unionId;

    @Schema(
            description = "Tencent risk assessment level",
            example = "低",
            allowableValues = {"高", "中", "低"}
    )
    private String txRiskLevel;

    @Schema(description = "UnionID login count in last 30 days", example = "0")
    private Integer unionIdLoginCount;

    @Schema(description = "Phone number login count in last 30 days", example = "2")
    private Integer phoneLoginCount;

    @Schema(description = "Member points", example = "1000")
    private Integer memberPoints;

    @Schema(description = "Is member frozen", example = "false")
    private Boolean isFrozenMember;
}
