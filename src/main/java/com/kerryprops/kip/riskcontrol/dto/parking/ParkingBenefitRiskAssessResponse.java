package com.kerryprops.kip.riskcontrol.dto.parking;

import com.kerryprops.kip.riskcontrol.constant.RiskResult;
import com.kerryprops.kip.riskcontrol.dto.IpGeolocationInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Parking benefits risk assessment response.
 */
@Data
@Schema(description = "Parking benefits risk assessment response")
public class ParkingBenefitRiskAssessResponse {

    @Schema(description = "Mall code", example = "JAKC")
    private String mallCode;

    @Schema(description = "IP address", example = "************")
    private String ipAddress;

    @Schema(description = "IP geolocation information")
    private IpGeolocationInfo ipGeolocation;

    @Schema(description = "Member points", example = "1000")
    private Integer memberPoints;

    @Schema(description = "Risk assessment result", example = "PASS")
    private RiskResult riskResult;

    @Schema(description = "Block message if rejected", example = "缴费地为非中国大陆，暂无法使用会员停车礼遇")
    private String blockMessage;

    @Schema(description = "Assessment details", example = "会员停车权益：非大陆IP被拦截")
    private String assessmentDetails;

    @Schema(description = "Assessment time")
    private LocalDateTime assessmentTime;
}
