package com.kerryprops.kip.riskcontrol.dto.marketing;

import com.kerryprops.kip.riskcontrol.constant.RiskResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Marketing scenario risk assessment response.
 */
@Data
@Schema(description = "Marketing scenario risk assessment response")
public class MarketingRiskAssessResponse {

    @Schema(description = "Mall code", example = "JAKC")
    private String mallCode;

    @Schema(description = "Member points", example = "1000")
    private Integer memberPoints;

    @Schema(description = "Risk assessment result", example = "PASS")
    private RiskResult riskResult;

    @Schema(description = "Block message if rejected")
    private String blockMessage;

    @Schema(description = "Assessment details", example = "营销场景：积分不足")
    private String assessmentDetails;

    @Schema(description = "Assessment time")
    private LocalDateTime assessmentTime;
}
