package com.kerryprops.kip.riskcontrol.rules;

import com.kerryprops.kip.riskcontrol.model.RiskAssessmentContext;
import com.kerryprops.kip.riskcontrol.constant.RiskResult;

/**
 * Drools rules for comprehensive risk assessment based on business requirements.
 *
 * Business Rules Implementation:
 *
 * IP归属地规则:
 * - 登录: 非大陆IP -> Y (不拦截)
 * - 会员停车权益: 非大陆IP -> N (拦截) - "缴费地为非中国大陆，暂无法使用会员停车礼遇"
 * - 拍照积分: 非大陆IP -> N (拦截) - "该功能无法使用，请至礼宾台"
 * - 营销场景: 非大陆IP -> / (无限制)
 *
 * Assessment Logic:
 * - PASS (Y): 不拦截，允许操作
 * - REJECT (N): 拦截，阻止操作并显示相应提示语
 *
 * 备注：Y表示不拦截，N表示拦截
 */

// ========== IP归属地规则 ==========

// 登录场景：非大陆IP -> Y (不拦截)
rule "Login - Non-mainland IP - PASS"
    salience 100
    when
        $context : RiskAssessmentContext(
            businessScenario == RiskAssessmentContext.BusinessScenario.LOGIN,
            ipGeolocation != null,
            ipGeolocation.fromMainlandChina == false,
            riskResult == null
        )
    then
        $context.setRiskResult(RiskResult.PASS);
        $context.setAssessmentDetails("登录场景：非大陆IP允许访问");
        System.out.println("Rule executed: Login - Non-mainland IP - PASS");
end

// 会员停车权益：非大陆IP -> N (拦截)
rule "Parking Benefit - Non-mainland IP - REJECT"
    salience 100
    when
        $context : RiskAssessmentContext(
            businessScenario == RiskAssessmentContext.BusinessScenario.PARKING_BENEFIT,
            isIpFromMainlandChina() == false,
            mallCode == RiskAssessmentContext.MallCode.JAKC || mallCode == RiskAssessmentContext.MallCode.HKC || mallCode == RiskAssessmentContext.MallCode.KP,
            riskResult == null
        )
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setBlockMessage("缴费地为非中国大陆，暂无法使用会员停车礼遇");
        $context.setAssessmentDetails("会员停车权益：非大陆IP被拦截");
        System.out.println("Rule executed: Parking Benefit - Non-mainland IP - REJECT");
end

// 拍照积分：非大陆IP -> N (拦截)
rule "Photo Points - Non-mainland IP - REJECT"
    salience 100
    when
        $context : RiskAssessmentContext(
            businessScenario == RiskAssessmentContext.BusinessScenario.PHOTO_POINTS,
            isIpFromMainlandChina() == false,
            mallCode == RiskAssessmentContext.MallCode.JAKC || mallCode == RiskAssessmentContext.MallCode.HKC || mallCode == RiskAssessmentContext.MallCode.KP,
            riskResult == null
        )
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setBlockMessage("该功能无法使用，请至礼宾台");
        $context.setAssessmentDetails("拍照积分：非大陆IP被拦截");
        System.out.println("Rule executed: Photo Points - Non-mainland IP - REJECT");
end

// ========== 手机号段规则 ==========

// 拍照积分：非大陆手机号 -> N (拦截)
rule "Photo Points - Non-mainland Phone - REJECT"
    salience 90
    when
        $context : RiskAssessmentContext(
            businessScenario == RiskAssessmentContext.BusinessScenario.PHOTO_POINTS,
            isMainlandPhoneNumber == false,
            mallCode == RiskAssessmentContext.MallCode.JAKC || mallCode == RiskAssessmentContext.MallCode.HKC,
            riskResult == null
        )
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setBlockMessage("手机号号段拍照积分受限，请您至礼宾台积分");
        $context.setAssessmentDetails("拍照积分：非大陆手机号被拦截");
        System.out.println("Rule executed: Photo Points - Non-mainland Phone - REJECT");
end

// 虚拟号段：登录 -> N (拦截)
rule "Login - Virtual Phone Number - REJECT"
    salience 90
    when
        $context : RiskAssessmentContext(
            businessScenario == RiskAssessmentContext.BusinessScenario.LOGIN,
            isVirtualPhoneNumber == true,
            mallCode == RiskAssessmentContext.MallCode.ALL,
            riskResult == null
        )
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setBlockMessage("无提示，收不到验证码");
        $context.setAssessmentDetails("登录：虚拟号段被拦截");
        System.out.println("Rule executed: Login - Virtual Phone Number - REJECT");
end

// 虚拟号段：拍照积分 -> N (拦截)
rule "Photo Points - Virtual Phone Number - REJECT"
    salience 90
    when
        $context : RiskAssessmentContext(
            businessScenario == RiskAssessmentContext.BusinessScenario.PHOTO_POINTS,
            isVirtualPhoneNumber == true,
            mallCode == RiskAssessmentContext.MallCode.JAKC || mallCode == RiskAssessmentContext.MallCode.HKC || mallCode == RiskAssessmentContext.MallCode.KP,
            riskResult == null
        )
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setBlockMessage("手机号号段拍照积分受限，请您至礼宾台积分");
        $context.setAssessmentDetails("拍照积分：虚拟号段被拦截");
        System.out.println("Rule executed: Photo Points - Virtual Phone Number - REJECT");
end

// ========== 积分相关规则 ==========

// 会员停车权益：负积分 -> N (拦截)
rule "Parking Benefit - Negative Points - REJECT"
    salience 80
    when
        $context : RiskAssessmentContext(
            businessScenario == RiskAssessmentContext.BusinessScenario.PARKING_BENEFIT,
            hasNegativePoints() == true,
            mallCode == RiskAssessmentContext.MallCode.JAKC || mallCode == RiskAssessmentContext.MallCode.HKC || mallCode == RiskAssessmentContext.MallCode.KP,
            riskResult == null
        )
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setBlockMessage("TBD");
        $context.setAssessmentDetails("会员停车权益：负积分被拦截");
        System.out.println("Rule executed: Parking Benefit - Negative Points - REJECT");
end

// 拍照积分：负积分 -> N (拦截)
rule "Photo Points - Negative Points - REJECT"
    salience 80
    when
        $context : RiskAssessmentContext(
            businessScenario == RiskAssessmentContext.BusinessScenario.PHOTO_POINTS,
            hasNegativePoints() == true,
            riskResult == null
        )
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setBlockMessage("TBD");
        $context.setAssessmentDetails("拍照积分：负积分被拦截");
        System.out.println("Rule executed: Photo Points - Negative Points - REJECT");
end

// 营销场景：负积分 -> N (拦截)
rule "Marketing - Negative Points - REJECT"
    salience 80
    when
        $context : RiskAssessmentContext(
            businessScenario == RiskAssessmentContext.BusinessScenario.MARKETING,
            hasNegativePoints() == true,
            mallCode == RiskAssessmentContext.MallCode.JAKC || mallCode == RiskAssessmentContext.MallCode.HKC || mallCode == RiskAssessmentContext.MallCode.KP,
            riskResult == null
        )
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setAssessmentDetails("营销场景：负积分被拦截");
        System.out.println("Rule executed: Marketing - Negative Points - REJECT");
end

// 销售积分：负积分 -> N (拦截)
rule "Sales Points - Negative Points - REJECT"
    salience 80
    when
        $context : RiskAssessmentContext(
            businessScenario == RiskAssessmentContext.BusinessScenario.SALES_POINTS,
            hasNegativePoints() == true,
            mallCode == RiskAssessmentContext.MallCode.JAKC || mallCode == RiskAssessmentContext.MallCode.HKC || mallCode == RiskAssessmentContext.MallCode.KP,
            riskResult == null
        )
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setAssessmentDetails("销售积分：负积分被拦截");
        System.out.println("Rule executed: Sales Points - Negative Points - REJECT");
end

// ========== ID校验规则 ==========

// ID校验：超限 -> N (拦截)
rule "Login - ID Validation Exceeded - REJECT"
    salience 70
    when
        $context : RiskAssessmentContext(
            businessScenario == RiskAssessmentContext.BusinessScenario.LOGIN,
            isIdValidationExceeded() == true,
            mallCode == RiskAssessmentContext.MallCode.ALL,
            riskResult == null
        )
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setBlockMessage("此微信登录会员账户已超过限额，您可登录近期使用过的会员账户");
        $context.setAssessmentDetails("登录：ID校验超限被拦截");
        System.out.println("Rule executed: Login - ID Validation Exceeded - REJECT");
end

// ========== 会员属性规则 ==========

// 冻结会员 -> N (拦截)
rule "Frozen Member - REJECT"
    salience 60
    when
        $context : RiskAssessmentContext(
            isFrozenMember == true,
            mallCode == RiskAssessmentContext.MallCode.ALL,
            riskResult == null
        )
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setBlockMessage("当前会员卡无法使用");
        $context.setAssessmentDetails("冻结会员被拦截");
        System.out.println("Rule executed: Frozen Member - REJECT");
end

// ========== 腾讯风控规则 ==========

// 腾讯风控：高风险 -> N (拦截)
rule "Tencent Risk High - REJECT"
    salience 50
    when
        $context : RiskAssessmentContext(
            isTencentRiskHigh() == true,
            mallCode == RiskAssessmentContext.MallCode.ALL,
            riskResult == null
        )
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setBlockMessage("无提示，收不到验证码");
        $context.setAssessmentDetails("腾讯风控：高风险被拦截");
        System.out.println("Rule executed: Tencent Risk High - REJECT");
end

// 腾讯风控：中风险 -> 手机验证码拦截
rule "Tencent Risk Medium - SMS Block"
    salience 50
    when
        $context : RiskAssessmentContext(
            isTencentRiskMedium() == true,
            mallCode == RiskAssessmentContext.MallCode.ALL,
            riskResult == null
        )
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setBlockMessage("无提示，收不到验证码");
        $context.setAssessmentDetails("腾讯风控：中风险手机验证码拦截");
        System.out.println("Rule executed: Tencent Risk Medium - SMS Block");
end

// ========== 默认规则 ==========

// 大陆IP默认通过规则
rule "Mainland IP Default - PASS"
    salience 10
    when
        $context : RiskAssessmentContext(
            isIpFromMainlandChina() == true,
            riskResult == null
        )
    then
        $context.setRiskResult(RiskResult.PASS);
        $context.setAssessmentDetails("大陆IP默认通过");
        System.out.println("Rule executed: Mainland IP Default - PASS");
end

// 最终默认拒绝规则
rule "Default REJECT"
    salience -100  // 最低优先级
    when
        $context : RiskAssessmentContext(riskResult == null)
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setAssessmentDetails("默认拒绝：未匹配到任何通过规则");
        System.out.println("Rule executed: Default REJECT");
end
